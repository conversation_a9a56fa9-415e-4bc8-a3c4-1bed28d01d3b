# Fix for Your Existing Backend API

## 🔍 **Issues Found**

Your backend API schema doesn't match what your frontend expects. Here are the specific mismatches:

### 1. **Endpoint Mismatch**
- **Your Backend**: `POST /api/account/profile-picture`
- **Frontend Expects**: `POST /api/account/avatar`

### 2. **Form Field Mismatch**
- **Your Backend**: expects `profilePicture` field
- **Frontend Sends**: `avatar` field

### 3. **Response Format Mismatch**
- **Your Backend Returns**:
  ```json
  {
    "success": true,
    "data": {
      "profilePictureUrl": "/api/account/profile-picture/*************-profile.jpg",
      "message": "Profile picture uploaded successfully"
    }
  }
  ```
- **Frontend Expects**:
  ```json
  {
    "avatar": "/api/account/profile-picture/*************-profile.jpg",
    "data": {
      "profilePictureUrl": "/api/account/profile-picture/*************-profile.jpg"
    }
  }
  ```

## 🛠️ **Quick Fix for Your Backend**

### Option A: Add New Route (Recommended)

Add this route to your existing `routes/account.js`:

```javascript
// Add this route alongside your existing profile-picture route
router.post('/avatar', authMiddleware, upload.single('avatar'), exports.uploadProfilePicture);
```

### Option B: Update Your Existing Controller

Update your `controllers/accountController.js` `uploadProfilePicture` function:

```javascript
exports.uploadProfilePicture = async (req, res, next) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'Please upload a profile picture'
      });
    }

    const user = await User.findById(req.user.id);
    
    // Delete old profile picture if exists
    if (user.profilePicture) {
      const oldImagePath = path.join(__dirname, '../uploads/profiles', user.profilePicture);
      if (fs.existsSync(oldImagePath)) {
        fs.unlinkSync(oldImagePath);
      }
    }

    // Update user with new profile picture
    user.profilePicture = req.file.filename;
    await user.save();

    const profilePictureUrl = `/api/account/profile-picture/${req.file.filename}`;

    // UPDATED: Return response in format that frontend expects
    res.status(200).json({
      success: true,
      avatar: profilePictureUrl,        // Frontend looks for this field
      data: {
        profilePictureUrl: profilePictureUrl,  // Your original format
        message: 'Profile picture uploaded successfully'
      }
    });

  } catch (err) {
    next(err);
  }
};
```

### Option C: Update Your Upload Middleware

Update your multer configuration to accept both field names:

```javascript
// In your upload middleware or route
const uploadFields = upload.fields([
  { name: 'avatar', maxCount: 1 },
  { name: 'profilePicture', maxCount: 1 }
]);

// Then in your route:
router.post('/avatar', authMiddleware, uploadFields, exports.uploadProfilePicture);
router.post('/profile-picture', authMiddleware, uploadFields, exports.uploadProfilePicture);

// And update your controller to handle both:
exports.uploadProfilePicture = async (req, res, next) => {
  try {
    // Handle both field names
    const file = req.files?.avatar?.[0] || req.files?.profilePicture?.[0] || req.file;
    
    if (!file) {
      return res.status(400).json({
        success: false,
        error: 'Please upload a profile picture'
      });
    }

    // ... rest of your existing code
    
    // Use the file object instead of req.file
    user.profilePicture = file.filename;
    await user.save();

    const profilePictureUrl = `/api/account/profile-picture/${file.filename}`;

    res.status(200).json({
      success: true,
      avatar: profilePictureUrl,        // Frontend expects this
      data: {
        profilePictureUrl: profilePictureUrl,
        message: 'Profile picture uploaded successfully'
      }
    });

  } catch (err) {
    next(err);
  }
};
```

## 🚀 **Simplest Solution (Recommended)**

The easiest fix is to add one line to your existing routes:

```javascript
// In your routes/account.js file, add this line:
router.post('/avatar', authMiddleware, upload.single('avatar'), exports.uploadProfilePicture);

// Keep your existing route too:
router.post('/profile-picture', authMiddleware, upload.single('profilePicture'), exports.uploadProfilePicture);
```

And update your controller response format:

```javascript
// In your controller, change the response to:
res.status(200).json({
  success: true,
  avatar: `/api/account/profile-picture/${req.file.filename}`,  // Add this line
  data: {
    profilePictureUrl: `/api/account/profile-picture/${req.file.filename}`,
    message: 'Profile picture uploaded successfully'
  }
});
```

## 🔧 **Why the Fallback Isn't Working**

Your frontend fallback to localStorage isn't working because:

1. **Wrong Error Detection**: Your frontend only falls back on 404 errors, but your API might be returning different error codes
2. **Endpoint Mismatch**: The frontend is calling `/api/account/avatar` but your backend only has `/api/account/profile-picture`

### Check Your Frontend Error Handling

Look at this code in `profile-model.js` line 99:

```javascript
// Check if it's a 404 error (endpoint not implemented)
if (error.message.includes('404')) {
  console.warn('Profile picture upload endpoint not available, using local storage fallback');
  // Fallback: Store as base64 in localStorage for now
  return this.uploadProfilePictureLocal(file);
}
```

If your backend is returning a different error (like 400, 500, or network error), the fallback won't trigger.

## 🧪 **Test Your Fix**

After implementing the fix, test with:

```bash
# Test the new endpoint
curl -X POST \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "avatar=@test-image.jpg" \
  https://apiww-production.up.railway.app/api/account/avatar

# Should return:
{
  "success": true,
  "avatar": "/api/account/profile-picture/filename.jpg",
  "data": {
    "profilePictureUrl": "/api/account/profile-picture/filename.jpg",
    "message": "Profile picture uploaded successfully"
  }
}
```

## 📝 **Summary**

The quickest fix is:

1. **Add new route**: `POST /api/account/avatar` that accepts `avatar` field
2. **Update response format**: Include `avatar` field at root level
3. **Keep existing route**: Don't break existing functionality

This will make your frontend work immediately without any frontend changes!
