# Backend Setup Guide for Profile Picture Upload

This guide provides the complete backend implementation needed to make the profile picture upload functionality work with your existing frontend.

## Overview

Your frontend is already configured to work with the profile picture API. You need to implement these endpoints on your backend server (`https://apiww-production.up.railway.app`):

- `POST /api/account/avatar` - Upload profile picture
- `GET /api/account/profile-picture/:filename` - Serve profile pictures

## Step 1: Install Dependencies

On your backend server, install the required packages:

```bash
npm install multer
```

Note: `path` and `fs` are built-in Node.js modules.

## Step 2: Create Directory Structure

Create these directories in your backend project:

```
backend/
├── middleware/
│   └── upload.js
├── controllers/
│   └── accountController.js
├── routes/
│   └── account.js
└── uploads/
    └── profiles/
```

## Step 3: Implementation Files

### File 1: `middleware/upload.js`

```javascript
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Ensure uploads directory exists
const uploadsDir = path.join(__dirname, '../uploads/profiles');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadsDir);
  },
  filename: function (req, file, cb) {
    // Generate unique filename: userId_timestamp.extension
    const userId = req.user?.id || 'anonymous';
    const timestamp = Date.now();
    const extension = path.extname(file.originalname);
    const filename = `${userId}_${timestamp}${extension}`;
    cb(null, filename);
  }
});

// File filter for images only
const fileFilter = (req, file, cb) => {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only JPEG, PNG, and GIF are allowed.'), false);
  }
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 2 * 1024 * 1024 // 2MB limit
  }
});

module.exports = upload;
```

### File 2: `controllers/accountController.js`

```javascript
const path = require('path');
const fs = require('fs');

// Upload avatar endpoint
exports.uploadAvatar = async (req, res, next) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No file uploaded'
      });
    }

    // Update user's avatar in database
    const userId = req.user.id;
    const avatarFilename = req.file.filename;
    
    // TODO: Update user record in your database
    // Example: await User.findByIdAndUpdate(userId, { avatar: avatarFilename });
    
    // Construct the avatar URL
    const avatarUrl = `/api/account/profile-picture/${avatarFilename}`;

    res.json({
      success: true,
      message: 'Avatar uploaded successfully',
      avatar: avatarUrl,
      filename: avatarFilename
    });

  } catch (error) {
    console.error('Avatar upload error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to upload avatar'
    });
  }
};

// Serve profile pictures
exports.getProfilePicture = async (req, res, next) => {
  try {
    const { filename } = req.params;
    
    // Validate filename to prevent directory traversal
    if (!filename || filename.includes('..') || filename.includes('/')) {
      return res.status(400).json({
        success: false,
        message: 'Invalid filename'
      });
    }

    const imagePath = path.join(__dirname, '../uploads/profiles', filename);
    
    // Check if file exists
    if (!fs.existsSync(imagePath)) {
      return res.status(404).json({
        success: false,
        message: 'Image not found'
      });
    }

    // Get file extension to set proper content type
    const ext = path.extname(filename).toLowerCase();
    const contentTypeMap = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.webp': 'image/webp'
    };

    const contentType = contentTypeMap[ext] || 'application/octet-stream';
    
    // Set headers
    res.setHeader('Content-Type', contentType);
    res.setHeader('Cache-Control', 'public, max-age=********'); // Cache for 1 year
    
    // Send file
    res.sendFile(imagePath);

  } catch (error) {
    console.error('Error serving profile picture:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to serve image'
    });
  }
};
```

### File 3: `routes/account.js`

```javascript
const express = require('express');
const router = express.Router();
const accountController = require('../controllers/accountController');
const upload = require('../middleware/upload');
const authMiddleware = require('../middleware/auth'); // Your existing auth middleware

// Avatar upload route (requires authentication)
router.post('/avatar', authMiddleware, upload.single('avatar'), accountController.uploadAvatar);

// Profile picture serving route (public)
router.get('/profile-picture/:filename', accountController.getProfilePicture);

module.exports = router;
```

## Step 4: Register Routes

In your main app file (e.g., `app.js` or `server.js`), make sure to register the account routes:

```javascript
const accountRoutes = require('./routes/account');
app.use('/api/account', accountRoutes);
```

## Step 5: Database Integration

Update the `uploadAvatar` function in `controllers/accountController.js` to save the avatar filename to your user database:

```javascript
// Replace the TODO comment with your actual database update logic
// Example for MongoDB with Mongoose:
// await User.findByIdAndUpdate(userId, { avatar: avatarFilename });

// Example for SQL databases:
// await db.query('UPDATE users SET avatar = ? WHERE id = ?', [avatarFilename, userId]);
```

## Step 6: Error Handling

Add proper error handling middleware to your Express app:

```javascript
// Error handling middleware
app.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        message: 'File too large. Maximum size is 2MB.'
      });
    }
  }
  
  res.status(500).json({
    success: false,
    message: error.message || 'Internal server error'
  });
});
```

## Step 7: Testing

Once implemented, test the endpoints:

1. **Upload Test**: POST to `/api/account/avatar` with a form-data file
2. **Serve Test**: GET `/api/account/profile-picture/{filename}`

## Security Considerations

1. **File Validation**: Only allow image files
2. **Size Limits**: 2MB maximum file size
3. **Path Traversal**: Validate filenames to prevent directory traversal
4. **Authentication**: Require authentication for uploads
5. **Rate Limiting**: Consider adding rate limiting for uploads

## Frontend Integration

Your frontend is already configured to work with these endpoints. The key files that handle the integration are:

- `src/scripts/pages/profile/profile-model.js` - Handles upload logic
- `src/scripts/services/avatar-service.js` - Manages avatar display
- `src/scripts/config.js` - API endpoint configuration

Once you implement the backend endpoints, the profile picture functionality should work seamlessly!
