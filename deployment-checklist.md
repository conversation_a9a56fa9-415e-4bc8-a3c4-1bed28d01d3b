# Profile Picture API Deployment Checklist

## Pre-Deployment Setup

### ✅ Backend Server Requirements

- [ ] Node.js backend server running (Express.js recommended)
- [ ] Access to your backend codebase at `https://apiww-production.up.railway.app`
- [ ] Database connection for storing user avatar filenames
- [ ] Authentication middleware already implemented

### ✅ Dependencies Installation

On your backend server, install:

```bash
npm install multer
```

### ✅ File Structure Creation

Create these files in your backend project:

- [ ] `middleware/upload.js`
- [ ] `controllers/accountController.js` (or update existing)
- [ ] `routes/account.js` (or update existing)
- [ ] `uploads/profiles/` directory

## Implementation Steps

### ✅ Step 1: Upload Middleware

- [ ] Copy the upload middleware code from `backend-setup-guide.md`
- [ ] Create `middleware/upload.js`
- [ ] Ensure uploads directory is created automatically

### ✅ Step 2: Controller Implementation

- [ ] Copy the controller code from `backend-setup-guide.md`
- [ ] Create or update `controllers/accountController.js`
- [ ] Add both `uploadAvatar` and `getProfilePicture` functions
- [ ] Update the database integration part with your actual database code

### ✅ Step 3: Route Configuration

- [ ] Copy the route code from `backend-setup-guide.md`
- [ ] Create or update `routes/account.js`
- [ ] Ensure authentication middleware is properly referenced
- [ ] Register routes in your main app file

### ✅ Step 4: Main App Integration

In your main app file (`app.js` or `server.js`):

- [ ] Import and register account routes: `app.use('/api/account', accountRoutes)`
- [ ] Add error handling middleware for multer errors
- [ ] Ensure CORS is configured to allow file uploads

## Database Integration

### ✅ User Model Update

Update your user model/schema to include avatar field:

**MongoDB/Mongoose:**
```javascript
const userSchema = new mongoose.Schema({
  // ... existing fields
  avatar: {
    type: String,
    default: null
  }
});
```

**SQL Database:**
```sql
ALTER TABLE users ADD COLUMN avatar VARCHAR(255);
```

### ✅ Database Operations

- [ ] Update the `uploadAvatar` function to save avatar filename to database
- [ ] Test database connection and update operations

## Security Configuration

### ✅ File Upload Security

- [ ] File type validation (only images)
- [ ] File size limits (2MB max)
- [ ] Filename sanitization
- [ ] Directory traversal prevention

### ✅ Authentication & Authorization

- [ ] Avatar upload requires authentication
- [ ] Profile picture serving is public (no auth required)
- [ ] Rate limiting for uploads (optional but recommended)

## Testing

### ✅ Local Testing

- [ ] Test avatar upload endpoint with Postman or similar tool
- [ ] Test profile picture serving endpoint
- [ ] Verify file storage in uploads/profiles directory
- [ ] Check database updates

### ✅ Frontend Integration Testing

- [ ] Test from your frontend application
- [ ] Verify avatar appears in navigation
- [ ] Test avatar updates across all components
- [ ] Check error handling for failed uploads

## Deployment

### ✅ Production Environment

- [ ] Deploy backend changes to Railway/your hosting platform
- [ ] Ensure uploads directory exists and is writable
- [ ] Configure environment variables if needed
- [ ] Test API endpoints in production

### ✅ File Storage Considerations

**For Railway/Heroku (Ephemeral File System):**
- [ ] Consider using cloud storage (AWS S3, Cloudinary, etc.) for production
- [ ] Files uploaded to local storage will be lost on app restart

**For Persistent Storage:**
- [ ] Ensure uploads directory is persistent
- [ ] Set up backup strategy for uploaded files

## Monitoring & Maintenance

### ✅ Logging

- [ ] Add logging for upload attempts
- [ ] Log file serving requests
- [ ] Monitor error rates

### ✅ Performance

- [ ] Set appropriate cache headers for images
- [ ] Consider CDN for image serving
- [ ] Monitor disk space usage

## Troubleshooting

### ✅ Common Issues

**Upload fails with 400 error:**
- [ ] Check if multer middleware is properly configured
- [ ] Verify file type and size limits
- [ ] Ensure authentication token is valid

**Images not serving:**
- [ ] Check if uploads directory exists
- [ ] Verify file permissions
- [ ] Check file path construction

**Frontend not updating:**
- [ ] Verify API response format matches frontend expectations
- [ ] Check avatar service event handling
- [ ] Clear browser cache

## Final Verification

### ✅ End-to-End Test

- [ ] Upload a profile picture from frontend
- [ ] Verify image appears in navigation immediately
- [ ] Refresh page and confirm image persists
- [ ] Test with different image formats (JPG, PNG, GIF)
- [ ] Test file size limits

### ✅ Cross-Browser Testing

- [ ] Test in Chrome
- [ ] Test in Firefox
- [ ] Test in Safari
- [ ] Test on mobile devices

## Post-Deployment

### ✅ Documentation

- [ ] Update API documentation
- [ ] Document any environment-specific configurations
- [ ] Create user guide for profile picture feature

### ✅ Backup Strategy

- [ ] Set up regular backups of uploads directory
- [ ] Document recovery procedures
- [ ] Test backup restoration process

---

## Quick Test Commands

After deployment, test these endpoints:

```bash
# Test image serving (should return 404 for non-existent file)
curl https://apiww-production.up.railway.app/api/account/profile-picture/test.jpg

# Test upload (requires authentication)
curl -X POST \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "avatar=@test-image.jpg" \
  https://apiww-production.up.railway.app/api/account/avatar
```

## Support

If you encounter issues:

1. Check server logs for error messages
2. Verify all files are properly uploaded to your backend
3. Test endpoints individually before testing frontend integration
4. Ensure your authentication middleware is working correctly

Your frontend is already fully configured and ready to work with these backend endpoints!
