# Profile Picture Integration Guide

This guide explains how to integrate the backend `getProfilePicture` function with the frontend avatar system.

## Backend Function

The backend provides a `getProfilePicture` function that serves profile images:

```javascript
// controllers/accountController.js
exports.getProfilePicture = async (req, res, next) => {
  try {
    const { filename } = req.params;
    const imagePath = path.join(__dirname, '../uploads/profiles', filename);

    // Check if file exists
    if (!fs.existsSync(imagePath)) {
      return res.status(404).json({
        success: false,
        error: 'Profile picture not found'
      });
    }

    // Get file extension to set proper content type
    const ext = path.extname(filename).toLowerCase();
    let contentType = 'image/jpeg'; // default

    switch (ext) {
      case '.png': contentType = 'image/png'; break;
      case '.gif': contentType = 'image/gif'; break;
      case '.webp': contentType = 'image/webp'; break;
    }

    res.setHeader('Content-Type', contentType);
    res.sendFile(imagePath);
  } catch (err) {
    next(err);
  }
};
```

## Frontend Integration

### 1. API Endpoint Configuration

The endpoint is already configured in `src/scripts/config.js`:

```javascript
ACCOUNT: {
  GET: "/api/account",
  UPDATE: "/api/account",
  DELETE: "/api/account",
  UPLOAD_AVATAR: "/api/account/avatar",
  GET_PROFILE_PICTURE: "/api/account/profile-picture", // New endpoint
}
```

### 2. Avatar Service Integration

The `avatarService` automatically handles profile picture URLs:

```javascript
import avatarService from '../services/avatar-service.js';

// Get avatar URL for current user
const currentUserAvatar = avatarService.getCurrentAvatarUrl();

// Get avatar URL for any user by filename
const userAvatar = avatarService.getAvatarUrlByFilename('user123_profile.jpg');

// Get complete user display info
const userInfo = avatarService.getUserDisplayInfoFromData({
  username: 'john',
  avatar: 'john_profile.png'
});
```

### 3. Using the Profile Picture Helper

```javascript
import ProfilePictureHelper from '../utils/profile-picture-helper.js';

// Create avatar element
const user = { username: 'alice', avatar: 'alice_profile.jpg' };
const avatarHtml = ProfilePictureHelper.createAvatarElement(user, {
  size: '50px',
  className: 'user-avatar'
});

// Render complete user profile
const profileHtml = ProfilePictureHelper.renderUserProfile(user, {
  showUsername: true,
  avatarSize: '60px',
  layout: 'horizontal'
});
```

## Backend Implementation

### 1. Install Required Dependencies

First, install the necessary packages on your backend server:

```bash
npm install multer path fs
```

### 2. Create Upload Middleware

Create `middleware/upload.js`:

```javascript
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Ensure uploads directory exists
const uploadsDir = path.join(__dirname, '../uploads/profiles');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadsDir);
  },
  filename: function (req, file, cb) {
    // Generate unique filename: userId_timestamp.extension
    const userId = req.user?.id || 'anonymous';
    const timestamp = Date.now();
    const extension = path.extname(file.originalname);
    const filename = `${userId}_${timestamp}${extension}`;
    cb(null, filename);
  }
});

// File filter for images only
const fileFilter = (req, file, cb) => {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only JPEG, PNG, and GIF are allowed.'), false);
  }
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 2 * 1024 * 1024 // 2MB limit
  }
});

module.exports = upload;
```

### 3. Create Account Controller

Create or update `controllers/accountController.js`:

```javascript
const path = require('path');
const fs = require('fs');

// Upload avatar endpoint
exports.uploadAvatar = async (req, res, next) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No file uploaded'
      });
    }

    // Update user's avatar in database
    const userId = req.user.id;
    const avatarFilename = req.file.filename;

    // TODO: Update user record in your database
    // Example: await User.findByIdAndUpdate(userId, { avatar: avatarFilename });

    // Construct the avatar URL
    const avatarUrl = `/api/account/profile-picture/${avatarFilename}`;

    res.json({
      success: true,
      message: 'Avatar uploaded successfully',
      avatar: avatarUrl,
      filename: avatarFilename
    });

  } catch (error) {
    console.error('Avatar upload error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to upload avatar'
    });
  }
};

// Serve profile pictures
exports.getProfilePicture = async (req, res, next) => {
  try {
    const { filename } = req.params;

    // Validate filename to prevent directory traversal
    if (!filename || filename.includes('..') || filename.includes('/')) {
      return res.status(400).json({
        success: false,
        message: 'Invalid filename'
      });
    }

    const imagePath = path.join(__dirname, '../uploads/profiles', filename);

    // Check if file exists
    if (!fs.existsSync(imagePath)) {
      return res.status(404).json({
        success: false,
        message: 'Image not found'
      });
    }

    // Get file extension to set proper content type
    const ext = path.extname(filename).toLowerCase();
    const contentTypeMap = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.webp': 'image/webp'
    };

    const contentType = contentTypeMap[ext] || 'application/octet-stream';

    // Set headers
    res.setHeader('Content-Type', contentType);
    res.setHeader('Cache-Control', 'public, max-age=********'); // Cache for 1 year

    // Send file
    res.sendFile(imagePath);

  } catch (error) {
    console.error('Error serving profile picture:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to serve image'
    });
  }
};
```

### 4. Update Routes

Add these routes to your Express router (`routes/account.js`):

```javascript
const express = require('express');
const router = express.Router();
const accountController = require('../controllers/accountController');
const upload = require('../middleware/upload');
const authMiddleware = require('../middleware/auth'); // Your auth middleware

// Avatar upload route (requires authentication)
router.post('/avatar', authMiddleware, upload.single('avatar'), accountController.uploadAvatar);

// Profile picture serving route (public)
router.get('/profile-picture/:filename', accountController.getProfilePicture);

module.exports = router;
```

## URL Structure

Profile pictures are served at:
```
GET /api/account/profile-picture/{filename}
```

Examples:
- `/api/account/profile-picture/user123_avatar.jpg`
- `/api/account/profile-picture/alice_profile.png`

## File Storage Structure

```
uploads/
└── profiles/
    ├── user123_avatar.jpg
    ├── alice_profile.png
    ├── bob_avatar.webp
    └── jane_profile.gif
```

## Frontend Usage Examples

### 1. Community Posts

```javascript
// Display post with author avatar
function renderPost(post) {
  const authorInfo = avatarService.getUserDisplayInfoFromData(post.author);

  return `
    <div class="post">
      <div class="post-header">
        <img src="${authorInfo.avatar}"
             alt="${authorInfo.username}"
             class="post-author-avatar"
             data-avatar="true">
        <span class="post-author">${authorInfo.username}</span>
      </div>
      <div class="post-content">${post.content}</div>
    </div>
  `;
}
```

### 2. User Lists

```javascript
// Display user list with avatars
function renderUserList(users) {
  return users.map(user => {
    const displayInfo = avatarService.getUserDisplayInfoFromData(user);
    return `
      <div class="user-item">
        ${displayInfo.hasCustomAvatar
          ? `<img src="${displayInfo.avatar}" alt="${displayInfo.username}" class="user-avatar">`
          : `<div class="user-initial">${displayInfo.initial}</div>`
        }
        <span class="username">${displayInfo.username}</span>
      </div>
    `;
  }).join('');
}
```

### 3. Comments Section

```javascript
// Display comment with user avatar
function renderComment(comment) {
  const avatarUrl = avatarService.getAvatarUrlByFilename(comment.user.avatar);

  return `
    <div class="comment">
      <img src="${avatarUrl}"
           alt="${comment.user.username}"
           class="comment-avatar"
           onerror="this.src='${avatarService.getDefaultAvatarUrl()}'">
      <div class="comment-content">
        <strong>${comment.user.username}</strong>
        <p>${comment.content}</p>
      </div>
    </div>
  `;
}
```

## Error Handling

The system includes automatic fallback handling:

1. **Invalid filename**: Falls back to default avatar
2. **File not found**: Backend returns 404, frontend shows default avatar
3. **Network error**: Frontend shows default avatar
4. **Invalid image**: Frontend shows user initial

## Performance Considerations

1. **Preloading**: Use `ProfilePictureHelper.preloadProfilePictures()` for user lists
2. **Caching**: Browser automatically caches images served with proper headers
3. **Lazy loading**: Consider implementing lazy loading for large user lists
4. **Image optimization**: Ensure uploaded images are properly resized on the backend

## Security Notes

1. **File validation**: Backend validates file extensions and content types
2. **Path traversal**: Backend uses `path.join()` to prevent directory traversal
3. **File existence**: Backend checks file existence before serving
4. **Content-Type**: Backend sets proper Content-Type headers

## Testing

Test the integration:

```javascript
// Test avatar URL generation
console.log(avatarService.getAvatarUrlByFilename('test.jpg'));
// Expected: "http://localhost:3000/api/account/profile-picture/test.jpg"

// Test user display info
const testUser = { username: 'test', avatar: 'test.jpg' };
console.log(avatarService.getUserDisplayInfoFromData(testUser));
// Expected: { avatar: "...", username: "test", initial: "T", hasCustomAvatar: true }
```
