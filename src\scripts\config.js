const CONFIG = {
  // Use local proxy in development (localhost), remote URL in production
  BASE_URL: window.location.hostname === 'localhost'
    ? "" // Empty string means use same origin (proxy will handle /api routes)
    : "https://apiww-production.up.railway.app",
  API_ENDPOINTS: {
    AUTH: {
      REGISTER: "/api/auth/register",
      LOGIN: "/api/auth/login",
      LOGOUT: "/api/auth/logout",
      GET_USER: "/api/auth/user",
      REFRESH_TOKEN: "/api/auth/refresh",
    },
    POSTS: {
      GET_ALL: "/api/posts",
      GET_BY_ID: "/api/posts/",
      CREATE: "/api/posts",
      UPDATE: "/api/posts/",
      DELETE: "/api/posts/",
    },
    COMMENTS: {
      GET_ALL: "/api/posts/{postId}/comments",
      CREATE: "/api/posts/{postId}/comments",
      UPDATE: "/api/posts/{postId}/comments/{commentId}",
      DELETE: "/api/posts/{postId}/comments/{commentId}",
    },
    ACCOUNT: {
      GET: "/api/account",
      UPDATE: "/api/account",
      DELETE: "/api/account",
      UPLOAD_AVATAR: "/api/account/avatar",
      GET_PROFILE_PICTURE: "/api/account/profile-picture",
    },
  },
  STORAGE_KEYS: {
    AUTH_TOKEN: "agriedu_auth_token",
    REFRESH_TOKEN: "agriedu_refresh_token",
    USER_DATA: "agriedu_user_data",
  },
};

export default CONFIG;
