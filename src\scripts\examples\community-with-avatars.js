/**
 * Example: Community Feature with Profile Pictures
 * 
 * This example demonstrates how to use the getProfilePicture backend function
 * to display user avatars in a community/social feature.
 */

import avatarService from '../services/avatar-service.js';
import ProfilePictureHelper from '../utils/profile-picture-helper.js';

/**
 * Example Community Component
 * Shows how to integrate profile pictures in a social feature
 */
export class CommunityExample {
  
  /**
   * Render a community post with author avatar
   * @param {Object} post - Post data from API
   * @returns {string} HTML string
   */
  static renderPost(post) {
    // Example post structure:
    // {
    //   id: "123",
    //   content: "Hello everyone!",
    //   author: {
    //     username: "john_doe",
    //     avatar: "john_doe_profile.jpg" // This filename will be served by getProfilePicture
    //   },
    //   createdAt: "2024-01-15T10:30:00Z",
    //   likes: 5,
    //   comments: []
    // }

    const authorInfo = avatarService.getUserDisplayInfoFromData(post.author);
    
    return `
      <div class="community-post" data-post-id="${post.id}">
        <div class="post-header">
          <div class="author-info">
            ${authorInfo.hasCustomAvatar 
              ? `<img src="${authorInfo.avatar}" 
                     alt="${authorInfo.username}'s profile" 
                     class="author-avatar"
                     data-avatar="true"
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                 <div class="author-avatar-fallback" style="display: none;">
                   ${authorInfo.initial}
                 </div>`
              : `<div class="author-avatar-initial">${authorInfo.initial}</div>`
            }
            <div class="author-details">
              <span class="author-name">${authorInfo.username}</span>
              <span class="post-time">${this.formatTime(post.createdAt)}</span>
            </div>
          </div>
        </div>
        
        <div class="post-content">
          ${post.content}
        </div>
        
        <div class="post-actions">
          <button class="like-btn" data-post-id="${post.id}">
            ❤️ ${post.likes}
          </button>
          <button class="comment-btn" data-post-id="${post.id}">
            💬 ${post.comments.length}
          </button>
        </div>
        
        <div class="comments-section">
          ${post.comments.map(comment => this.renderComment(comment)).join('')}
        </div>
      </div>
    `;
  }

  /**
   * Render a comment with user avatar
   * @param {Object} comment - Comment data
   * @returns {string} HTML string
   */
  static renderComment(comment) {
    // Example comment structure:
    // {
    //   id: "456",
    //   content: "Great post!",
    //   author: {
    //     username: "jane_smith",
    //     avatar: "jane_smith_avatar.png"
    //   },
    //   createdAt: "2024-01-15T11:00:00Z"
    // }

    return ProfilePictureHelper.renderUserProfile(comment.author, {
      showUsername: true,
      avatarSize: '32px',
      layout: 'horizontal',
      className: 'comment-author'
    }) + `
      <div class="comment-content">
        <p>${comment.content}</p>
        <span class="comment-time">${this.formatTime(comment.createdAt)}</span>
      </div>
    `;
  }

  /**
   * Render user list (e.g., online users, followers)
   * @param {Array} users - Array of user objects
   * @returns {string} HTML string
   */
  static renderUserList(users) {
    // Preload avatars for better performance
    const avatarFilenames = users.map(user => user.avatar).filter(Boolean);
    ProfilePictureHelper.preloadProfilePictures(avatarFilenames);

    return `
      <div class="user-list">
        <h3>Community Members</h3>
        ${users.map(user => `
          <div class="user-item" data-username="${user.username}">
            ${ProfilePictureHelper.renderUserProfile(user, {
              showUsername: true,
              avatarSize: '40px',
              layout: 'horizontal',
              className: 'user-profile'
            })}
            <div class="user-status ${user.isOnline ? 'online' : 'offline'}">
              ${user.isOnline ? '🟢' : '⚫'}
            </div>
          </div>
        `).join('')}
      </div>
    `;
  }

  /**
   * Example of handling API response with user data
   * @param {Array} apiPosts - Posts from API
   * @returns {string} HTML string
   */
  static renderCommunityFeed(apiPosts) {
    // Example API response:
    // [
    //   {
    //     id: "1",
    //     content: "Check out this amazing plant!",
    //     author: {
    //       username: "plant_lover",
    //       avatar: "plant_lover_123.jpg" // This will be served by getProfilePicture
    //     },
    //     createdAt: "2024-01-15T09:00:00Z",
    //     likes: 12,
    //     comments: [...]
    //   }
    // ]

    return `
      <div class="community-feed">
        <h2>Community Feed</h2>
        ${apiPosts.map(post => this.renderPost(post)).join('')}
      </div>
    `;
  }

  /**
   * Handle real-time avatar updates
   * @param {string} username - Username that updated their avatar
   * @param {string} newAvatarFilename - New avatar filename
   */
  static handleAvatarUpdate(username, newAvatarFilename) {
    // Update all instances of this user's avatar on the page
    ProfilePictureHelper.updateUserAvatars(username, newAvatarFilename);
    
    // You could also emit an event for other components to listen to
    window.dispatchEvent(new CustomEvent('userAvatarUpdated', {
      detail: { username, avatar: newAvatarFilename }
    }));
  }

  /**
   * Example of fetching and displaying community data
   */
  static async loadCommunityData() {
    try {
      // Simulate API call
      const response = await fetch('/api/posts');
      const posts = await response.json();
      
      // Each post.author.avatar contains a filename like "user123_profile.jpg"
      // The avatarService will automatically construct the correct URL:
      // http://localhost:3000/api/account/profile-picture/user123_profile.jpg
      
      const feedHtml = this.renderCommunityFeed(posts);
      document.getElementById('community-container').innerHTML = feedHtml;
      
    } catch (error) {
      console.error('Error loading community data:', error);
    }
  }

  /**
   * Format timestamp for display
   * @param {string} timestamp - ISO timestamp
   * @returns {string} Formatted time
   */
  static formatTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) return 'just now';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`;
    if (diff < ********) return `${Math.floor(diff / 3600000)}h ago`;
    return date.toLocaleDateString();
  }
}

/**
 * CSS Styles for the community component
 * Add this to your CSS file:
 */
export const communityStyles = `
.community-post {
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  background: white;
}

.post-header {
  margin-bottom: 12px;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.author-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
}

.author-avatar-initial,
.author-avatar-fallback {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: #007bff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 18px;
}

.author-details {
  display: flex;
  flex-direction: column;
}

.author-name {
  font-weight: 600;
  color: #1da1f2;
}

.post-time {
  font-size: 14px;
  color: #657786;
}

.post-content {
  margin: 12px 0;
  line-height: 1.5;
}

.post-actions {
  display: flex;
  gap: 16px;
  padding-top: 12px;
  border-top: 1px solid #e1e8ed;
}

.like-btn, .comment-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.like-btn:hover, .comment-btn:hover {
  background-color: #f7f9fa;
}

.user-list {
  background: white;
  border-radius: 8px;
  padding: 16px;
}

.user-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #e1e8ed;
}

.user-item:last-child {
  border-bottom: none;
}

.user-status.online {
  color: #1da1f2;
}

.user-status.offline {
  color: #657786;
}
`;

// Usage example:
// const posts = await fetch('/api/posts').then(r => r.json());
// document.getElementById('feed').innerHTML = CommunityExample.renderCommunityFeed(posts);
