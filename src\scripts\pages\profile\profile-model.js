import { updateData, uploadFile, getData } from '../../data/api.js';
import { saveSetupData, getAllSetupData } from '../../utils/indexeddb.js';
import authService from '../../data/auth-service.js';
import avatarService from '../../services/avatar-service.js';
import CONFIG from '../../config.js';

const ProfileModel = {
  async getUserProfile() {
    const setupData = await getAllSetupData();
    const userData = authService.getUserData();

    // Use the global avatar service to get avatar
    const avatar = avatarService.getCurrentAvatarUrl();

    // Try to get fresh avatar from API if authenticated
    if (authService.isAuthenticated()) {
      try {
        const accountData = await getData(CONFIG.API_ENDPOINTS.ACCOUNT.GET, true);
        const apiAvatar = accountData?.avatar || accountData?.data?.avatar;

        if (apiAvatar && apiAvatar !== avatar) {
          // Update avatar in auth service if we got a different one from API
          authService.updateUserAvatar(apiAvatar);
          return {
            avatar: apiAvatar,
            fullName: setupData[0]?.name || '',
            username: userData?.username || '',
            experience: setupData[0]?.experience || '',
          };
        }
      } catch (error) {
        console.warn('Failed to fetch avatar from API:', error);
      }
    }

    return {
      avatar: avatar,
      fullName: setupData[0]?.name || '',
      username: userData?.username || '',
      experience: setupData[0]?.experience || '',
    };
  },

  getDefaultAvatarUrl() {
    return avatarService.getDefaultAvatarUrl();
  },

  async uploadProfilePicture(file) {
    const userData = authService.getUserData();

    if (!userData) {
      throw new Error('User belum login');
    }

    // Validate file
    if (!file) {
      throw new Error('File tidak ditemukan');
    }

    const maxSize = 2 * 1024 * 1024; // 2MB
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];

    if (file.size > maxSize) {
      throw new Error('Ukuran file maksimal 2MB');
    }

    if (!allowedTypes.includes(file.type)) {
      throw new Error('Format file harus berupa gambar (JPEG, PNG, atau GIF)');
    }

    // Create FormData for file upload
    const formData = new FormData();
    formData.append('avatar', file);

    try {
      // Try to upload to API first
      const response = await uploadFile(CONFIG.API_ENDPOINTS.ACCOUNT.UPLOAD_AVATAR, formData, true);
      
      console.log('API upload response:', response); // Add this debug log
      
      // Get the avatar URL from response - handle both old and new response formats
      const avatarUrl = response.avatar || 
                        response.data?.avatar || 
                        response.avatarUrl || 
                        response.data?.profilePictureUrl;
                        
      console.log('Extracted avatar URL:', avatarUrl); // Add this debug log
      
      // Update avatar using the global avatar service
      if (avatarUrl) {
        avatarService.updateAvatar(avatarUrl);
      }

      return response;
    } catch (error) {
      console.error('Error uploading profile picture to API:', error);

      // Check if it's a 404 error (endpoint not implemented)
      if (error.message.includes('404')) {
        console.warn('Profile picture upload endpoint not available, using local storage fallback');

        // Fallback: Store as base64 in localStorage for now
        return this.uploadProfilePictureLocal(file);
      }

      // For other errors, throw them
      throw error;
    }
  },

  async uploadProfilePictureLocal(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = function(e) {
        try {
          const base64Data = e.target.result;

          // Update avatar using the global avatar service
          avatarService.updateAvatar(base64Data);

          console.log('Profile picture stored locally as fallback');
          resolve({
            success: true,
            avatar: base64Data,
            message: 'Foto profil disimpan sementara (server endpoint belum tersedia)'
          });
        } catch (error) {
          reject(new Error('Gagal memproses gambar: ' + error.message));
        }
      };

      reader.onerror = function() {
        reject(new Error('Gagal membaca file gambar'));
      };

      reader.readAsDataURL(file);
    });
  },

async updateUsername(newUsername) {
  const userData = authService.getUserData();

  if (!userData) {
    throw new Error('User belum login');
  }

  if (newUsername === userData.username) {
    // Username tidak berubah, skip update API
    return;
  }

  // Panggil API PUT /api/account untuk update username
  const updatedUser = await updateData('/api/account', {
    username: newUsername,
    email: userData.email,  // asumsi masih wajib dikirim
  });

  // Ambil token dan refreshToken yang sudah ada
  const token = authService.getToken();
  const refreshToken = authService.getRefreshToken();

  // Simpan kembali data lengkap agar tidak hilang
  authService.saveAuthData({
    token,
    refreshToken,
    user: {
      ...userData,
      username: updatedUser.username,
    },
  });
},


  async saveSetupData({ name, experience }) {
    // Ambil data setup lama dulu, jika ada
    const setupDataArr = await getAllSetupData();

    let id = setupDataArr[0]?.id;

    // Kalau belum ada data, buat baru
    const dataToSave = {
      id, // kalau undefined, IndexedDB auto increment
      name,
      experience,
    };

    await saveSetupData(dataToSave);
  },
};

export default ProfileModel;
