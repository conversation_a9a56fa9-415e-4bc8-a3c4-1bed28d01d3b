import authService from '../data/auth-service.js';
import ProfileModel from '../pages/profile/profile-model.js';
import CONFIG from '../config.js';

/**
 * Global Avatar Service
 * Manages avatar display and updates across the entire application
 */
class AvatarService {
  constructor() {
    this.defaultAvatarUrl = 'images/avatar.jpg';
    this.avatarUpdateListeners = new Set();
    this.setupGlobalEventListeners();
  }

  /**
   * Get the current user's avatar URL with proper fallback logic
   * @returns {string} Avatar URL or default avatar
   */
  getCurrentAvatarUrl() {
    try {
      // Try to get from auth service first
      const avatarUrl = authService.getUserAvatar();
      if (avatarUrl && this.isValidImageUrl(avatarUrl)) {
        // If it's a filename (not a full URL or base64), construct the API URL
        if (this.isProfilePictureFilename(avatarUrl)) {
          return this.constructProfilePictureUrl(avatarUrl);
        }
        return avatarUrl;
      }

      // Fallback to profile model
      const profileAvatar = ProfileModel.getDefaultAvatarUrl();
      if (profileAvatar && this.isValidImageUrl(profileAvatar)) {
        return profileAvatar;
      }

      // Final fallback
      return this.defaultAvatarUrl;
    } catch (error) {
      console.warn('Error getting current avatar URL:', error);
      return this.defaultAvatarUrl;
    }
  }

  /**
   * Check if a string is a profile picture filename (not a full URL or base64)
   * @param {string} url - URL to check
   * @returns {boolean} True if it's a filename
   */
  isProfilePictureFilename(url) {
    if (!url || typeof url !== 'string') return false;

    // Not a data URL
    if (url.startsWith('data:')) return false;

    // Not a full HTTP/HTTPS URL
    if (url.startsWith('http://') || url.startsWith('https://')) return false;

    // Not a relative path starting with / or ./
    if (url.startsWith('/') || url.startsWith('./') || url.startsWith('../')) return false;

    // Check if it looks like a filename with extension
    return url.match(/^[^\/]+\.(jpg|jpeg|png|gif|webp)$/i);
  }

  /**
   * Construct the full API URL for a profile picture filename
   * @param {string} filename - Profile picture filename
   * @returns {string} Full API URL
   */
  constructProfilePictureUrl(filename) {
    return `${CONFIG.BASE_URL}${CONFIG.API_ENDPOINTS.ACCOUNT.GET_PROFILE_PICTURE}/${filename}`;
  }

  /**
   * Get avatar URL for any user by filename
   * @param {string} filename - Profile picture filename
   * @returns {string} Avatar URL or default avatar
   */
  getAvatarUrlByFilename(filename) {
    if (!filename || typeof filename !== 'string') {
      return this.defaultAvatarUrl;
    }

    // If it's a filename, construct the API URL
    if (this.isProfilePictureFilename(filename)) {
      return this.constructProfilePictureUrl(filename);
    }

    // If it's already a full URL or base64, return as is
    if (this.isValidImageUrl(filename)) {
      return filename;
    }

    // Fallback to default
    return this.defaultAvatarUrl;
  }

  /**
   * Get user display information including avatar and initials
   * @returns {Object} User display info
   */
  async getUserDisplayInfo() {
    try {
      const userData = authService.getUserData();
      const avatarUrl = this.getCurrentAvatarUrl();

      let username = 'User';
      if (userData && userData.username) {
        username = userData.username;
      } else {
        // Fallback to localStorage
        username = localStorage.getItem("user_name") || "User";
      }

      return {
        avatar: avatarUrl,
        username: username,
        initial: username.charAt(0).toUpperCase(),
        hasCustomAvatar: avatarUrl !== this.defaultAvatarUrl
      };
    } catch (error) {
      console.warn('Error getting user display info:', error);
      return {
        avatar: this.defaultAvatarUrl,
        username: 'User',
        initial: 'U',
        hasCustomAvatar: false
      };
    }
  }

  /**
   * Update avatar and notify all components
   * @param {string} avatarUrl - New avatar URL
   */
  updateAvatar(avatarUrl) {
    try {
      // Update in auth service (this will trigger the global event)
      authService.updateUserAvatar(avatarUrl);

      // Update all avatar elements immediately
      this.updateAllAvatarElements(avatarUrl);

      console.log('Avatar updated globally:', avatarUrl);
    } catch (error) {
      console.error('Error updating avatar:', error);
    }
  }

  /**
   * Update all avatar elements in the current page
   * @param {string} avatarUrl - Avatar URL to set
   */
  updateAllAvatarElements(avatarUrl) {
    const finalAvatarUrl = avatarUrl || this.defaultAvatarUrl;

    // Update all avatar images
    const avatarElements = document.querySelectorAll('img[data-avatar="true"], #avatarPreview, #sidebarAvatar, .avatar-image, .profile-avatar');
    avatarElements.forEach(element => {
      this.setAvatarWithFallback(element, finalAvatarUrl);
    });

    // Update navigation bar profile icons
    this.updateNavigationAvatars(finalAvatarUrl);
  }

  /**
   * Update navigation bar avatars
   * @param {string} avatarUrl - Avatar URL to set
   */
  updateNavigationAvatars(avatarUrl) {
    // Update profile icons in navigation
    const profileIcons = document.querySelectorAll('.app-profile-icon, .profile-icon');
    profileIcons.forEach(icon => {
      if (avatarUrl && avatarUrl !== this.defaultAvatarUrl) {
        // Replace text initial with image
        icon.innerHTML = `<img src="${avatarUrl}" alt="Profile" class="profile-avatar-img" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">`;
      } else {
        // Keep as initial if no custom avatar
        const userInfo = authService.getUserData();
        const username = userInfo?.username || localStorage.getItem("user_name") || "User";
        icon.textContent = username.charAt(0).toUpperCase();
      }
    });
  }

  /**
   * Set avatar with proper error handling and fallback
   * @param {HTMLImageElement} element - Image element to update
   * @param {string} avatarUrl - Avatar URL to set
   */
  setAvatarWithFallback(element, avatarUrl) {
    if (!element || element.tagName !== 'IMG') return;

    // Remove any existing error handlers
    element.onerror = null;

    // Set the new source
    element.src = avatarUrl;

    // Add error handler for fallback
    element.onerror = () => {
      if (element.src !== this.defaultAvatarUrl) {
        console.warn('Avatar failed to load, falling back to default:', avatarUrl);
        element.src = this.defaultAvatarUrl;
      }
    };
  }

  /**
   * Check if a URL is a valid image URL
   * @param {string} url - URL to check
   * @returns {boolean} True if valid
   */
  isValidImageUrl(url) {
    if (!url || typeof url !== 'string') return false;

    // Check for data URLs (base64 images)
    if (url.startsWith('data:image/')) return true;

    // Check for HTTP/HTTPS URLs
    if (url.startsWith('http://') || url.startsWith('https://')) return true;

    // Check for relative paths
    if (url.startsWith('/') || url.startsWith('./') || url.startsWith('../')) return true;

    // Check for simple filenames
    if (url.match(/\.(jpg|jpeg|png|gif|webp|svg)$/i)) return true;

    return false;
  }

  /**
   * Setup global event listeners for avatar updates
   */
  setupGlobalEventListeners() {
    // Listen for avatar update events
    window.addEventListener('avatarUpdated', (event) => {
      const { avatarUrl } = event.detail;
      this.updateAllAvatarElements(avatarUrl);
    });

    // Listen for page navigation to refresh avatars
    window.addEventListener('hashchange', () => {
      setTimeout(() => {
        const currentAvatar = this.getCurrentAvatarUrl();
        this.updateAllAvatarElements(currentAvatar);
      }, 100); // Small delay to ensure DOM is ready
    });
  }

  /**
   * Initialize avatars on page load
   */
  initializeAvatars() {
    const currentAvatar = this.getCurrentAvatarUrl();
    this.updateAllAvatarElements(currentAvatar);
  }

  /**
   * Add a listener for avatar updates
   * @param {Function} callback - Callback function
   */
  addAvatarUpdateListener(callback) {
    this.avatarUpdateListeners.add(callback);
  }

  /**
   * Remove a listener for avatar updates
   * @param {Function} callback - Callback function
   */
  removeAvatarUpdateListener(callback) {
    this.avatarUpdateListeners.delete(callback);
  }

  /**
   * Get default avatar URL
   * @returns {string} Default avatar URL
   */
  getDefaultAvatarUrl() {
    return this.defaultAvatarUrl;
  }

  /**
   * Get user display information for any user by their avatar filename
   * This is useful for displaying other users' profiles in community features
   * @param {Object} userData - User data object containing username and avatar filename
   * @param {string} userData.username - Username
   * @param {string} userData.avatar - Avatar filename or URL
   * @returns {Object} User display info
   */
  getUserDisplayInfoFromData(userData) {
    try {
      const username = userData?.username || 'User';
      const avatarFilename = userData?.avatar;

      let avatarUrl = this.defaultAvatarUrl;
      let hasCustomAvatar = false;

      if (avatarFilename) {
        avatarUrl = this.getAvatarUrlByFilename(avatarFilename);
        hasCustomAvatar = avatarUrl !== this.defaultAvatarUrl;
      }

      return {
        avatar: avatarUrl,
        username: username,
        initial: username.charAt(0).toUpperCase(),
        hasCustomAvatar: hasCustomAvatar
      };
    } catch (error) {
      console.warn('Error getting user display info from data:', error);
      return {
        avatar: this.defaultAvatarUrl,
        username: userData?.username || 'User',
        initial: (userData?.username || 'U').charAt(0).toUpperCase(),
        hasCustomAvatar: false
      };
    }
  }
}

// Create and export singleton instance
const avatarService = new AvatarService();
export default avatarService;
