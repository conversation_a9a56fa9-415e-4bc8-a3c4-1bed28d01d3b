import avatarService from '../services/avatar-service.js';

/**
 * Profile Picture Helper Utilities
 * 
 * This module provides helper functions to work with the backend getProfilePicture function.
 * The backend function serves profile pictures from the uploads/profiles directory.
 * 
 * Backend function reference:
 * exports.getProfilePicture = async (req, res, next) => {
 *   const { filename } = req.params;
 *   const imagePath = path.join(__dirname, '../uploads/profiles', filename);
 *   // ... serves the image file with proper content-type headers
 * }
 */

/**
 * Helper class for working with profile pictures
 */
export class ProfilePictureHelper {
  
  /**
   * Get profile picture URL for a user
   * @param {string|Object} userDataOrFilename - User data object or filename string
   * @returns {string} Profile picture URL
   * 
   * @example
   * // Using with filename
   * const avatarUrl = ProfilePictureHelper.getProfilePictureUrl('user123_avatar.jpg');
   * 
   * // Using with user data object
   * const user = { username: 'john', avatar: 'john_profile.png' };
   * const avatarUrl = ProfilePictureHelper.getProfilePictureUrl(user);
   */
  static getProfilePictureUrl(userDataOrFilename) {
    if (typeof userDataOrFilename === 'string') {
      // It's a filename
      return avatarService.getAvatarUrlByFilename(userDataOrFilename);
    } else if (typeof userDataOrFilename === 'object' && userDataOrFilename !== null) {
      // It's a user data object
      return avatarService.getAvatarUrlByFilename(userDataOrFilename.avatar);
    }
    
    return avatarService.getDefaultAvatarUrl();
  }

  /**
   * Create an avatar image element for a user
   * @param {Object} userData - User data object
   * @param {Object} options - Options for the image element
   * @returns {string} HTML string for the avatar image
   * 
   * @example
   * const user = { username: 'jane', avatar: 'jane_avatar.jpg' };
   * const avatarHtml = ProfilePictureHelper.createAvatarElement(user, {
   *   size: '50px',
   *   className: 'user-avatar',
   *   showFallback: true
   * });
   */
  static createAvatarElement(userData, options = {}) {
    const {
      size = '40px',
      className = 'profile-avatar',
      showFallback = true,
      style = ''
    } = options;

    const displayInfo = avatarService.getUserDisplayInfoFromData(userData);
    
    if (displayInfo.hasCustomAvatar) {
      return `<img 
        src="${displayInfo.avatar}" 
        alt="${displayInfo.username}'s profile" 
        class="${className}" 
        data-avatar="true"
        style="width: ${size}; height: ${size}; border-radius: 50%; object-fit: cover; ${style}"
        onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
      >
      ${showFallback ? `<div class="${className}-fallback" style="display: none; width: ${size}; height: ${size}; border-radius: 50%; background: #007bff; color: white; align-items: center; justify-content: center; font-weight: bold; ${style}">${displayInfo.initial}</div>` : ''}`;
    } else {
      return `<div 
        class="${className}-initial" 
        style="width: ${size}; height: ${size}; border-radius: 50%; background: #007bff; color: white; display: flex; align-items: center; justify-content: center; font-weight: bold; ${style}"
      >${displayInfo.initial}</div>`;
    }
  }

  /**
   * Render user profile information with avatar
   * @param {Object} userData - User data object
   * @param {Object} options - Rendering options
   * @returns {string} HTML string for user profile display
   * 
   * @example
   * const user = { username: 'bob', avatar: 'bob_profile.png' };
   * const profileHtml = ProfilePictureHelper.renderUserProfile(user, {
   *   showUsername: true,
   *   avatarSize: '60px',
   *   layout: 'horizontal'
   * });
   */
  static renderUserProfile(userData, options = {}) {
    const {
      showUsername = true,
      avatarSize = '50px',
      layout = 'horizontal', // 'horizontal' or 'vertical'
      className = 'user-profile'
    } = options;

    const displayInfo = avatarService.getUserDisplayInfoFromData(userData);
    const avatarHtml = this.createAvatarElement(userData, { 
      size: avatarSize, 
      className: `${className}-avatar` 
    });

    const flexDirection = layout === 'vertical' ? 'column' : 'row';
    const gap = layout === 'vertical' ? '8px' : '12px';

    return `
      <div class="${className}" style="display: flex; flex-direction: ${flexDirection}; align-items: center; gap: ${gap};">
        ${avatarHtml}
        ${showUsername ? `<span class="${className}-username" style="font-weight: 500;">${displayInfo.username}</span>` : ''}
      </div>
    `;
  }

  /**
   * Update all avatar elements on the page for a specific user
   * @param {string} username - Username to update
   * @param {string} newAvatarFilename - New avatar filename
   * 
   * @example
   * // When a user updates their profile picture
   * ProfilePictureHelper.updateUserAvatars('john', 'john_new_avatar.jpg');
   */
  static updateUserAvatars(username, newAvatarFilename) {
    const newAvatarUrl = this.getProfilePictureUrl(newAvatarFilename);
    
    // Find all avatar elements for this user
    const avatarElements = document.querySelectorAll(`[data-username="${username}"]`);
    
    avatarElements.forEach(element => {
      if (element.tagName === 'IMG') {
        avatarService.setAvatarWithFallback(element, newAvatarUrl);
      } else {
        // Update container with new avatar HTML
        const userData = { username, avatar: newAvatarFilename };
        const newAvatarHtml = this.createAvatarElement(userData);
        element.innerHTML = newAvatarHtml;
      }
    });
  }

  /**
   * Preload profile pictures for better performance
   * @param {Array} filenames - Array of profile picture filenames
   * 
   * @example
   * // Preload avatars for users in a list
   * const userAvatars = users.map(user => user.avatar).filter(Boolean);
   * ProfilePictureHelper.preloadProfilePictures(userAvatars);
   */
  static preloadProfilePictures(filenames) {
    filenames.forEach(filename => {
      if (filename && typeof filename === 'string') {
        const img = new Image();
        img.src = this.getProfilePictureUrl(filename);
      }
    });
  }
}

// Export default instance for convenience
export default ProfilePictureHelper;

/**
 * Usage Examples:
 * 
 * 1. Display user avatar in a post:
 * ```javascript
 * const post = { 
 *   author: { username: 'alice', avatar: 'alice_profile.jpg' },
 *   content: 'Hello world!'
 * };
 * 
 * const avatarHtml = ProfilePictureHelper.createAvatarElement(post.author, {
 *   size: '40px',
 *   className: 'post-author-avatar'
 * });
 * ```
 * 
 * 2. Display user list with avatars:
 * ```javascript
 * const users = [
 *   { username: 'john', avatar: 'john_avatar.jpg' },
 *   { username: 'jane', avatar: 'jane_avatar.png' },
 *   { username: 'bob', avatar: null } // Will show initial
 * ];
 * 
 * const userListHtml = users.map(user => 
 *   ProfilePictureHelper.renderUserProfile(user, {
 *     showUsername: true,
 *     avatarSize: '50px',
 *     layout: 'horizontal'
 *   })
 * ).join('');
 * ```
 * 
 * 3. Get avatar URL for API responses:
 * ```javascript
 * // When receiving user data from API
 * const apiResponse = { username: 'user123', avatar: 'user123_profile.jpg' };
 * const avatarUrl = ProfilePictureHelper.getProfilePictureUrl(apiResponse.avatar);
 * ```
 */
