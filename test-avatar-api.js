/**
 * Test Script for Avatar API Endpoints
 * 
 * This script helps test the avatar upload and serving functionality
 * Run with: node test-avatar-api.js
 */

const fs = require('fs');
const path = require('path');

// Test configuration
const API_BASE_URL = 'https://apiww-production.up.railway.app';
const TEST_IMAGE_PATH = './test-image.jpg'; // You'll need to provide a test image

/**
 * Test the avatar upload endpoint
 */
async function testAvatarUpload() {
  console.log('🧪 Testing Avatar Upload...');
  
  try {
    // Check if test image exists
    if (!fs.existsSync(TEST_IMAGE_PATH)) {
      console.log('❌ Test image not found. Please add a test image at:', TEST_IMAGE_PATH);
      return;
    }

    // Create FormData for file upload
    const FormData = require('form-data');
    const form = new FormData();
    form.append('avatar', fs.createReadStream(TEST_IMAGE_PATH));

    // Make the upload request
    const fetch = require('node-fetch');
    const response = await fetch(`${API_BASE_URL}/api/account/avatar`, {
      method: 'POST',
      body: form,
      headers: {
        // Add your authentication token here
        'Authorization': 'Bearer YOUR_TEST_TOKEN_HERE',
        ...form.getHeaders()
      }
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ Avatar upload successful!');
      console.log('📄 Response:', result);
      
      // Test serving the uploaded image
      if (result.filename) {
        await testImageServing(result.filename);
      }
    } else {
      console.log('❌ Avatar upload failed!');
      console.log('📄 Error:', result);
    }

  } catch (error) {
    console.log('❌ Upload test error:', error.message);
  }
}

/**
 * Test the image serving endpoint
 */
async function testImageServing(filename) {
  console.log('\n🧪 Testing Image Serving...');
  
  try {
    const fetch = require('node-fetch');
    const response = await fetch(`${API_BASE_URL}/api/account/profile-picture/${filename}`);
    
    if (response.ok) {
      console.log('✅ Image serving successful!');
      console.log('📄 Content-Type:', response.headers.get('content-type'));
      console.log('📄 Content-Length:', response.headers.get('content-length'));
    } else {
      console.log('❌ Image serving failed!');
      console.log('📄 Status:', response.status, response.statusText);
    }

  } catch (error) {
    console.log('❌ Image serving test error:', error.message);
  }
}

/**
 * Test endpoint availability
 */
async function testEndpointAvailability() {
  console.log('🧪 Testing Endpoint Availability...');
  
  try {
    const fetch = require('node-fetch');
    
    // Test a non-existent image to check if endpoint exists
    const response = await fetch(`${API_BASE_URL}/api/account/profile-picture/non-existent.jpg`);
    
    if (response.status === 404) {
      console.log('✅ Profile picture endpoint is available (returns 404 for non-existent files)');
    } else if (response.status === 500) {
      console.log('⚠️  Endpoint exists but may have implementation issues');
    } else {
      console.log('📄 Unexpected response:', response.status, response.statusText);
    }

  } catch (error) {
    if (error.code === 'ENOTFOUND') {
      console.log('❌ Server not reachable. Check if the backend server is running.');
    } else {
      console.log('❌ Availability test error:', error.message);
    }
  }
}

/**
 * Main test function
 */
async function runTests() {
  console.log('🚀 Starting Avatar API Tests\n');
  console.log('🔗 Testing against:', API_BASE_URL);
  console.log('=' .repeat(50));
  
  // Test endpoint availability first
  await testEndpointAvailability();
  
  console.log('\n' + '=' .repeat(50));
  
  // Test upload (requires authentication token)
  await testAvatarUpload();
  
  console.log('\n' + '=' .repeat(50));
  console.log('🏁 Tests completed!');
  
  console.log('\n📝 Notes:');
  console.log('- Make sure to replace YOUR_TEST_TOKEN_HERE with a valid auth token');
  console.log('- Add a test image file at:', TEST_IMAGE_PATH);
  console.log('- Ensure your backend server is running and accessible');
}

// Install required dependencies check
function checkDependencies() {
  const requiredPackages = ['node-fetch', 'form-data'];
  const missing = [];
  
  for (const pkg of requiredPackages) {
    try {
      require(pkg);
    } catch (error) {
      missing.push(pkg);
    }
  }
  
  if (missing.length > 0) {
    console.log('❌ Missing required packages. Please install:');
    console.log(`npm install ${missing.join(' ')}`);
    process.exit(1);
  }
}

// Run the tests
if (require.main === module) {
  checkDependencies();
  runTests().catch(console.error);
}

module.exports = {
  testAvatarUpload,
  testImageServing,
  testEndpointAvailability
};
